# Hebrew translations for humanize package.
# Copyright (C) 2023
# This file is distributed under the same license as the humanizepackage.
# <PERSON> <<EMAIL>>, 2023.
#
msgid ""
msgstr ""
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-10-05 14:07-0400\n"
"PO-Revision-Date: 2023-10-05 15:45-0400\n"
"Last-Translator: <PERSON> <<EMAIL>>\n"
"Language-Team: Hebrew <<EMAIL>>\n"
"Language: he\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: src/humanize/number.py:83
msgctxt "0 (male)"
msgid "th"
msgstr "י"

#: src/humanize/number.py:84
msgctxt "1 (male)"
msgid "st"
msgstr "ון"

#: src/humanize/number.py:85
msgctxt "2 (male)"
msgid "nd"
msgstr "י"

#: src/humanize/number.py:86
msgctxt "3 (male)"
msgid "rd"
msgstr "י"

#: src/humanize/number.py:87
msgctxt "4 (male)"
msgid "th"
msgstr "י"

#: src/humanize/number.py:88
msgctxt "5 (male)"
msgid "th"
msgstr "י"

#: src/humanize/number.py:89
msgctxt "6 (male)"
msgid "th"
msgstr "י"

#: src/humanize/number.py:90
msgctxt "7 (male)"
msgid "th"
msgstr "י"

#: src/humanize/number.py:91
msgctxt "8 (male)"
msgid "th"
msgstr "י"

#: src/humanize/number.py:92
msgctxt "9 (male)"
msgid "th"
msgstr "י"

#: src/humanize/number.py:96
msgctxt "0 (female)"
msgid "th"
msgstr "ית"

#: src/humanize/number.py:97
msgctxt "1 (female)"
msgid "st"
msgstr "ונה"

#: src/humanize/number.py:98
msgctxt "2 (female)"
msgid "nd"
msgstr "יה"

#: src/humanize/number.py:99
msgctxt "3 (female)"
msgid "rd"
msgstr "ית"

#: src/humanize/number.py:100
msgctxt "4 (female)"
msgid "th"
msgstr "ית"

#: src/humanize/number.py:101
msgctxt "5 (female)"
msgid "th"
msgstr "ית"

#: src/humanize/number.py:102
msgctxt "6 (female)"
msgid "th"
msgstr "ית"

#: src/humanize/number.py:103
msgctxt "7 (female)"
msgid "th"
msgstr "ית"

#: src/humanize/number.py:104
msgctxt "8 (female)"
msgid "th"
msgstr "ית"

#: src/humanize/number.py:105
msgctxt "9 (female)"
msgid "th"
msgstr "ית"

#: src/humanize/number.py:178
msgid "thousand"
msgid_plural "thousand"
msgstr[0] "אלף"
msgstr[1] "אלפים"

#: src/humanize/number.py:179
msgid "million"
msgid_plural "million"
msgstr[0] "מיליון"
msgstr[1] "מיליון"

#: src/humanize/number.py:180
msgid "billion"
msgid_plural "billion"
msgstr[0] "מיליארד"
msgstr[1] "מיליארד"

#: src/humanize/number.py:181
msgid "trillion"
msgid_plural "trillion"
msgstr[0] "טריליון"
msgstr[1] "טריליון"

#: src/humanize/number.py:182
msgid "quadrillion"
msgid_plural "quadrillion"
msgstr[0] "קוודריליון"
msgstr[1] "קוודריליון"

#: src/humanize/number.py:183
msgid "quintillion"
msgid_plural "quintillion"
msgstr[0] "קווינטיליון"
msgstr[1] "קווינטיליון"

#: src/humanize/number.py:184
msgid "sextillion"
msgid_plural "sextillion"
msgstr[0] "סקסטיליון"
msgstr[1] "סקסטיליון"

#: src/humanize/number.py:185
msgid "septillion"
msgid_plural "septillion"
msgstr[0] "ספטיליון"
msgstr[1] "ספטיליון"

#: src/humanize/number.py:186
msgid "octillion"
msgid_plural "octillion"
msgstr[0] "אוקטיליון"
msgstr[1] "אוקטיליון"

#: src/humanize/number.py:187
msgid "nonillion"
msgid_plural "nonillion"
msgstr[0] "נוניליון"
msgstr[1] "נוניליון"

#: src/humanize/number.py:188
msgid "decillion"
msgid_plural "decillion"
msgstr[0] "דציליון"
msgstr[1] "דציליון"

#: src/humanize/number.py:189
msgid "googol"
msgid_plural "googol"
msgstr[0] "גוגול"
msgstr[1] "גוגול"

#: src/humanize/number.py:304
msgid "zero"
msgstr "אפס"

#: src/humanize/number.py:305
msgid "one"
msgstr "אחת"

#: src/humanize/number.py:306
msgid "two"
msgstr "שתיים"

#: src/humanize/number.py:307
msgid "three"
msgstr "שלוש"

#: src/humanize/number.py:308
msgid "four"
msgstr "ארבע"

#: src/humanize/number.py:309
msgid "five"
msgstr "חמש"

#: src/humanize/number.py:310
msgid "six"
msgstr "שש"

#: src/humanize/number.py:311
msgid "seven"
msgstr "שבע"

#: src/humanize/number.py:312
msgid "eight"
msgstr "שמונה"

#: src/humanize/number.py:313
msgid "nine"
msgstr "תשע"

#: src/humanize/time.py:151
#, python-format
msgid "%d microsecond"
msgid_plural "%d microseconds"
msgstr[0] "מיקרו־שנייה %d‏"
msgstr[1] "‏%d מיקרו־שניות"

#: src/humanize/time.py:160
#, python-format
msgid "%d millisecond"
msgid_plural "%d milliseconds"
msgstr[0] "מילי־שנייה %d‏"
msgstr[1] "‏%d מילי־שניות"

#: src/humanize/time.py:163 src/humanize/time.py:262
msgid "a moment"
msgstr "רגע"

#: src/humanize/time.py:166
msgid "a second"
msgstr "שנייה"

#: src/humanize/time.py:169
#, python-format
msgid "%d second"
msgid_plural "%d seconds"
msgstr[0] "שנייה %d‏"
msgstr[1] "‏%d שניות"

#: src/humanize/time.py:172
msgid "a minute"
msgstr "דקה"

#: src/humanize/time.py:176
#, python-format
msgid "%d minute"
msgid_plural "%d minutes"
msgstr[0] "דקה %d‏"
msgstr[1] "‏%d דקות"

#: src/humanize/time.py:179
msgid "an hour"
msgstr "שעה"

#: src/humanize/time.py:183
#, python-format
msgid "%d hour"
msgid_plural "%d hours"
msgstr[0] "שעה %d‏"
msgstr[1] "‏%d שעות"

#: src/humanize/time.py:187
msgid "a day"
msgstr "יום"

#: src/humanize/time.py:190 src/humanize/time.py:193
#, python-format
msgid "%d day"
msgid_plural "%d days"
msgstr[0] "יום %d‏"
msgstr[1] "‏%d ימים"

#: src/humanize/time.py:196
msgid "a month"
msgstr "חודש"

#: src/humanize/time.py:198
#, python-format
msgid "%d month"
msgid_plural "%d months"
msgstr[0] "חודש %d‏"
msgstr[1] "‏%d חודשים"

#: src/humanize/time.py:202
msgid "a year"
msgstr "שנה"

#: src/humanize/time.py:205 src/humanize/time.py:216
#, python-format
msgid "1 year, %d day"
msgid_plural "1 year, %d days"
msgstr[0] "שנה אחת, יום %d‏"
msgstr[1] "שנה אחת, %d ימים"

#: src/humanize/time.py:209
msgid "1 year, 1 month"
msgstr "שנה אחת, חודש אחד"

#: src/humanize/time.py:212
#, python-format
msgid "1 year, %d month"
msgid_plural "1 year, %d months"
msgstr[0] "שנה אחת, חודש %d‏"
msgstr[1] "שנה אחת, %d חודשים"

#: src/humanize/time.py:218
#, python-format
msgid "%d year"
msgid_plural "%d years"
msgstr[0] "שנה %d‏"
msgstr[1] "‏%d שנים"

#: src/humanize/time.py:259
#, python-format
msgid "%s from now"
msgstr "אחרי %s"

#: src/humanize/time.py:259
#, python-format
msgid "%s ago"
msgstr "לפני %s"

#: src/humanize/time.py:263
msgid "now"
msgstr "עכשיו"

#: src/humanize/time.py:296
msgid "today"
msgstr "היום"

#: src/humanize/time.py:299
msgid "tomorrow"
msgstr "מחר"

#: src/humanize/time.py:302
msgid "yesterday"
msgstr "אתמול"

#: src/humanize/time.py:612
#, python-format
msgid "%s and %s"
msgstr "%s ו%s"
