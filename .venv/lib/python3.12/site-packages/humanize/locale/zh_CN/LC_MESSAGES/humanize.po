# Simplified Chinese (China) translation for the project
# Copyright (C) 2016
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, 2016.
# <PERSON><PERSON> SUN <<EMAIL>>, 2019.
#
msgid ""
msgstr ""
"Project-Id-Version: 1.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-01-08 19:22+0200\n"
"PO-Revision-Date: 2016-11-14 23:02+0000\n"
"Last-Translator: <PERSON><PERSON> SUN <<EMAIL>>\n"
"Language-Team: Chinese (simplified)\n"
"Language: zh_CN\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#: src/humanize/number.py:84
msgctxt "0 (male)"
msgid "th"
msgstr "第"

#: src/humanize/number.py:85
msgctxt "1 (male)"
msgid "st"
msgstr "第"

#: src/humanize/number.py:86
msgctxt "2 (male)"
msgid "nd"
msgstr "第"

#: src/humanize/number.py:87
msgctxt "3 (male)"
msgid "rd"
msgstr "第"

#: src/humanize/number.py:88
msgctxt "4 (male)"
msgid "th"
msgstr "第"

#: src/humanize/number.py:89
msgctxt "5 (male)"
msgid "th"
msgstr "第"

#: src/humanize/number.py:90
msgctxt "6 (male)"
msgid "th"
msgstr "第"

#: src/humanize/number.py:91
msgctxt "7 (male)"
msgid "th"
msgstr "第"

#: src/humanize/number.py:92
msgctxt "8 (male)"
msgid "th"
msgstr "第"

#: src/humanize/number.py:93
msgctxt "9 (male)"
msgid "th"
msgstr "第"

#: src/humanize/number.py:97
msgctxt "0 (female)"
msgid "th"
msgstr "第"

#: src/humanize/number.py:98
msgctxt "1 (female)"
msgid "st"
msgstr "第"

#: src/humanize/number.py:99
msgctxt "2 (female)"
msgid "nd"
msgstr "第"

#: src/humanize/number.py:100
msgctxt "3 (female)"
msgid "rd"
msgstr "第"

#: src/humanize/number.py:101
msgctxt "4 (female)"
msgid "th"
msgstr "第"

#: src/humanize/number.py:102
msgctxt "5 (female)"
msgid "th"
msgstr "第"

#: src/humanize/number.py:103
msgctxt "6 (female)"
msgid "th"
msgstr "第"

#: src/humanize/number.py:104
msgctxt "7 (female)"
msgid "th"
msgstr "第"

#: src/humanize/number.py:105
msgctxt "8 (female)"
msgid "th"
msgstr "第"

#: src/humanize/number.py:106
msgctxt "9 (female)"
msgid "th"
msgstr "第"

#: src/humanize/number.py:178
msgid "thousand"
msgid_plural "thousand"
msgstr[0] "千"
msgstr[1] "千"

#: src/humanize/number.py:179
msgid "million"
msgid_plural "million"
msgstr[0] "百万"
msgstr[1] "百万"

#: src/humanize/number.py:180
msgid "billion"
msgid_plural "billion"
msgstr[0] "十亿"
msgstr[1] "十亿"

#: src/humanize/number.py:181
msgid "trillion"
msgid_plural "trillion"
msgstr[0] "兆"
msgstr[1] "兆"

#: src/humanize/number.py:182
msgid "quadrillion"
msgid_plural "quadrillion"
msgstr[0] "万亿"
msgstr[1] "万亿"

#: src/humanize/number.py:183
msgid "quintillion"
msgid_plural "quintillion"
msgstr[0] "百京"
msgstr[1] "百京"

#: src/humanize/number.py:184
msgid "sextillion"
msgid_plural "sextillion"
msgstr[0] "十垓"
msgstr[1] "十垓"

#: src/humanize/number.py:185
msgid "septillion"
msgid_plural "septillion"
msgstr[0] "秭"
msgstr[1] "秭"

#: src/humanize/number.py:186
msgid "octillion"
msgid_plural "octillion"
msgstr[0] "千秭"
msgstr[1] "千秭"

#: src/humanize/number.py:187
msgid "nonillion"
msgid_plural "nonillion"
msgstr[0] "百穰"
msgstr[1] "百穰"

#: src/humanize/number.py:188
msgid "decillion"
msgid_plural "decillion"
msgstr[0] "十沟"
msgstr[1] "十沟"

#: src/humanize/number.py:189
msgid "googol"
msgid_plural "googol"
msgstr[0] "古高尔"
msgstr[1] "古高尔"

#: src/humanize/number.py:301
msgid "zero"
msgstr ""

#: src/humanize/number.py:302
msgid "one"
msgstr "一"

#: src/humanize/number.py:303
msgid "two"
msgstr "二"

#: src/humanize/number.py:304
msgid "three"
msgstr "三"

#: src/humanize/number.py:305
msgid "four"
msgstr "四"

#: src/humanize/number.py:306
msgid "five"
msgstr "五"

#: src/humanize/number.py:307
msgid "six"
msgstr "六"

#: src/humanize/number.py:308
msgid "seven"
msgstr "七"

#: src/humanize/number.py:309
msgid "eight"
msgstr "八"

#: src/humanize/number.py:310
msgid "nine"
msgstr "九"

#: src/humanize/time.py:152
#, python-format
msgid "%d microsecond"
msgid_plural "%d microseconds"
msgstr[0] ""
msgstr[1] ""

#: src/humanize/time.py:161
#, python-format
msgid "%d millisecond"
msgid_plural "%d milliseconds"
msgstr[0] ""
msgstr[1] ""

#: src/humanize/time.py:164 src/humanize/time.py:259
msgid "a moment"
msgstr "一会儿"

#: src/humanize/time.py:167
msgid "a second"
msgstr "1秒"

#: src/humanize/time.py:170
#, python-format
msgid "%d second"
msgid_plural "%d seconds"
msgstr[0] "%d秒"
msgstr[1] "%d秒"

#: src/humanize/time.py:173
msgid "a minute"
msgstr "1分"

#: src/humanize/time.py:177
#, python-format
msgid "%d minute"
msgid_plural "%d minutes"
msgstr[0] "%d分"
msgstr[1] "%d分"

#: src/humanize/time.py:180
msgid "an hour"
msgstr "1小时"

#: src/humanize/time.py:184
#, python-format
msgid "%d hour"
msgid_plural "%d hours"
msgstr[0] "%d小时"
msgstr[1] "%d小时"

#: src/humanize/time.py:188
msgid "a day"
msgstr "1天"

#: src/humanize/time.py:191 src/humanize/time.py:194
#, python-format
msgid "%d day"
msgid_plural "%d days"
msgstr[0] "%d天"
msgstr[1] "%d天"

#: src/humanize/time.py:197
msgid "a month"
msgstr "1月"

#: src/humanize/time.py:199
#, python-format
msgid "%d month"
msgid_plural "%d months"
msgstr[0] "%d月"
msgstr[1] "%d月"

#: src/humanize/time.py:203
msgid "a year"
msgstr "1年"

#: src/humanize/time.py:206 src/humanize/time.py:217
#, python-format
msgid "1 year, %d day"
msgid_plural "1 year, %d days"
msgstr[0] "1年又%d天"
msgstr[1] "1年又%d天"

#: src/humanize/time.py:210
msgid "1 year, 1 month"
msgstr "1年又1月"

#: src/humanize/time.py:213
#, python-format
msgid "1 year, %d month"
msgid_plural "1 year, %d months"
msgstr[0] "1年又%d月"
msgstr[1] "1年又%d月"

#: src/humanize/time.py:219
#, python-format
msgid "%d year"
msgid_plural "%d years"
msgstr[0] "%d年"
msgstr[1] "%d年"

#: src/humanize/time.py:256
#, python-format
msgid "%s from now"
msgstr "%s之后"

#: src/humanize/time.py:256
#, python-format
msgid "%s ago"
msgstr "%s之前"

#: src/humanize/time.py:260
msgid "now"
msgstr "现在"

#: src/humanize/time.py:284
msgid "today"
msgstr "今天"

#: src/humanize/time.py:287
msgid "tomorrow"
msgstr "明天"

#: src/humanize/time.py:290
msgid "yesterday"
msgstr "昨天"

#: src/humanize/time.py:600
#, python-format
msgid "%s and %s"
msgstr ""
