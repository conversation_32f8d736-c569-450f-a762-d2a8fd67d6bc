# Danish translations for humanize package.
# Copyright (C) 2021
# This file is distributed under the same license as the humanize project.
# <AUTHOR> <EMAIL>, 2021.
#
msgid ""
msgstr ""
"Project-Id-Version: humanize\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-01-08 19:22+0200\n"
"PO-Revision-Date: 2021-11-24 22:25+0200\n"
"Last-Translator: YURII DEREVYCH <<EMAIL>>\n"
"Language-Team: Da\n"
"Language: da\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.0\n"

#: src/humanize/number.py:84
msgctxt "0 (male)"
msgid "th"
msgstr ":e"

#: src/humanize/number.py:85
msgctxt "1 (male)"
msgid "st"
msgstr ":e"

#: src/humanize/number.py:86
msgctxt "2 (male)"
msgid "nd"
msgstr ":t"

#: src/humanize/number.py:87
msgctxt "3 (male)"
msgid "rd"
msgstr ":e"

#: src/humanize/number.py:88
msgctxt "4 (male)"
msgid "th"
msgstr ":e"

#: src/humanize/number.py:89
msgctxt "5 (male)"
msgid "th"
msgstr ":e"

#: src/humanize/number.py:90
msgctxt "6 (male)"
msgid "th"
msgstr ":e"

#: src/humanize/number.py:91
msgctxt "7 (male)"
msgid "th"
msgstr ":e"

#: src/humanize/number.py:92
msgctxt "8 (male)"
msgid "th"
msgstr ":e"

#: src/humanize/number.py:93
msgctxt "9 (male)"
msgid "th"
msgstr ":e"

#: src/humanize/number.py:97
msgctxt "0 (female)"
msgid "th"
msgstr ":e"

#: src/humanize/number.py:98
msgctxt "1 (female)"
msgid "st"
msgstr ":e"

#: src/humanize/number.py:99
msgctxt "2 (female)"
msgid "nd"
msgstr ":t"

#: src/humanize/number.py:100
msgctxt "3 (female)"
msgid "rd"
msgstr ":e"

#: src/humanize/number.py:101
msgctxt "4 (female)"
msgid "th"
msgstr ":e"

#: src/humanize/number.py:102
msgctxt "5 (female)"
msgid "th"
msgstr ":e"

#: src/humanize/number.py:103
msgctxt "6 (female)"
msgid "th"
msgstr ":e"

#: src/humanize/number.py:104
msgctxt "7 (female)"
msgid "th"
msgstr ":e"

#: src/humanize/number.py:105
msgctxt "8 (female)"
msgid "th"
msgstr ":e"

#: src/humanize/number.py:106
msgctxt "9 (female)"
msgid "th"
msgstr ":e"

#: src/humanize/number.py:178
msgid "thousand"
msgid_plural "thousand"
msgstr[0] "tusind"
msgstr[1] "thousand"

#: src/humanize/number.py:179
msgid "million"
msgid_plural "million"
msgstr[0] "million"
msgstr[1] "millioner"

#: src/humanize/number.py:180
msgid "billion"
msgid_plural "billion"
msgstr[0] "billion"
msgstr[1] "billioner"

#: src/humanize/number.py:181
msgid "trillion"
msgid_plural "trillion"
msgstr[0] "billion"
msgstr[1] "billioner"

#: src/humanize/number.py:182
msgid "quadrillion"
msgid_plural "quadrillion"
msgstr[0] "quadrillion"
msgstr[1] "quadrillioner"

#: src/humanize/number.py:183
msgid "quintillion"
msgid_plural "quintillion"
msgstr[0] "quintillion"
msgstr[1] "quintillioner"

#: src/humanize/number.py:184
msgid "sextillion"
msgid_plural "sextillion"
msgstr[0] "sextillion"
msgstr[1] "sextillioner"

#: src/humanize/number.py:185
msgid "septillion"
msgid_plural "septillion"
msgstr[0] "septillion"
msgstr[1] "septillioner"

#: src/humanize/number.py:186
msgid "octillion"
msgid_plural "octillion"
msgstr[0] "octillion"
msgstr[1] "octillioner"

#: src/humanize/number.py:187
msgid "nonillion"
msgid_plural "nonillion"
msgstr[0] "nonillion"
msgstr[1] "nonillioner"

#: src/humanize/number.py:188
msgid "decillion"
msgid_plural "decillion"
msgstr[0] "decillion"
msgstr[1] "decillioner"

#: src/humanize/number.py:189
msgid "googol"
msgid_plural "googol"
msgstr[0] "googol"
msgstr[1] "googol"

#: src/humanize/number.py:301
msgid "zero"
msgstr "nul"

#: src/humanize/number.py:302
msgid "one"
msgstr "en"

#: src/humanize/number.py:303
msgid "two"
msgstr "to"

#: src/humanize/number.py:304
msgid "three"
msgstr "tre"

#: src/humanize/number.py:305
msgid "four"
msgstr "fire"

#: src/humanize/number.py:306
msgid "five"
msgstr "fem"

#: src/humanize/number.py:307
msgid "six"
msgstr "seks"

#: src/humanize/number.py:308
msgid "seven"
msgstr "syv"

#: src/humanize/number.py:309
msgid "eight"
msgstr "otte"

#: src/humanize/number.py:310
msgid "nine"
msgstr "ni"

#: src/humanize/time.py:152
#, python-format
msgid "%d microsecond"
msgid_plural "%d microseconds"
msgstr[0] "%d mikrosekund"
msgstr[1] "%d mikrosekunder"

#: src/humanize/time.py:161
#, python-format
msgid "%d millisecond"
msgid_plural "%d milliseconds"
msgstr[0] "%d millsekund"
msgstr[1] "%d millsekunder"

#: src/humanize/time.py:164 src/humanize/time.py:259
msgid "a moment"
msgstr "et øjeblik"

#: src/humanize/time.py:167
msgid "a second"
msgstr "en anden"

#: src/humanize/time.py:170
#, python-format
msgid "%d second"
msgid_plural "%d seconds"
msgstr[0] "%d sekund"
msgstr[1] "%d sekunder"

#: src/humanize/time.py:173
msgid "a minute"
msgstr "en minut"

#: src/humanize/time.py:177
#, python-format
msgid "%d minute"
msgid_plural "%d minutes"
msgstr[0] "%d minut"
msgstr[1] "%d minutter"

#: src/humanize/time.py:180
msgid "an hour"
msgstr "en time"

#: src/humanize/time.py:184
#, python-format
msgid "%d hour"
msgid_plural "%d hours"
msgstr[0] "%d time"
msgstr[1] "%d timer"

#: src/humanize/time.py:188
msgid "a day"
msgstr "en dag"

#: src/humanize/time.py:191 src/humanize/time.py:194
#, python-format
msgid "%d day"
msgid_plural "%d days"
msgstr[0] "%d dag"
msgstr[1] "%d dage"

#: src/humanize/time.py:197
msgid "a month"
msgstr "en måned"

#: src/humanize/time.py:199
#, python-format
msgid "%d month"
msgid_plural "%d months"
msgstr[0] "%d måned"
msgstr[1] "%d måneder"

#: src/humanize/time.py:203
msgid "a year"
msgstr "et år"

#: src/humanize/time.py:206 src/humanize/time.py:217
#, python-format
msgid "1 year, %d day"
msgid_plural "1 year, %d days"
msgstr[0] "1 år, %d dag"
msgstr[1] "1 år, %d dage"

#: src/humanize/time.py:210
msgid "1 year, 1 month"
msgstr "1 år, 1 måned"

#: src/humanize/time.py:213
#, python-format
msgid "1 year, %d month"
msgid_plural "1 year, %d months"
msgstr[0] "1 år, %d måned"
msgstr[1] "1 år, %d måneder"

#: src/humanize/time.py:219
#, python-format
msgid "%d year"
msgid_plural "%d years"
msgstr[0] "%d år"
msgstr[1] "%d år"

#: src/humanize/time.py:256
#, python-format
msgid "%s from now"
msgstr "%s fra nu"

#: src/humanize/time.py:256
#, python-format
msgid "%s ago"
msgstr "%s siden"

#: src/humanize/time.py:260
msgid "now"
msgstr "nu"

#: src/humanize/time.py:284
msgid "today"
msgstr "i dag"

#: src/humanize/time.py:287
msgid "tomorrow"
msgstr "i morgen"

#: src/humanize/time.py:290
msgid "yesterday"
msgstr "i går"

#: src/humanize/time.py:600
#, python-format
msgid "%s and %s"
msgstr "%s og %s"
