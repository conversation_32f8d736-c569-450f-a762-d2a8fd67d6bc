# Dutch (Netherlands) translations for PROJECT.
# Copyright (C) 2020 ORGANIZATION
# This file is distributed under the same license as the PROJECT project.
# <AUTHOR> <EMAIL>, 2013.
#
msgid ""
msgstr ""
"Project-Id-Version: PROJECT VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-01-08 19:22+0200\n"
"PO-Revision-Date: 2015-03-25 21:08+0100\n"
"Last-Translator: <PERSON>\n"
"Language-Team: nl_NL\n"
"Language: nl_NL\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"Generated-By: Babel 0.9.6\n"
"X-Generator: Poedit 1.7.5\n"

#: src/humanize/number.py:84
msgctxt "0 (male)"
msgid "th"
msgstr "de"

#: src/humanize/number.py:85
msgctxt "1 (male)"
msgid "st"
msgstr "ste"

#: src/humanize/number.py:86
msgctxt "2 (male)"
msgid "nd"
msgstr "de"

#: src/humanize/number.py:87
msgctxt "3 (male)"
msgid "rd"
msgstr "de"

#: src/humanize/number.py:88
msgctxt "4 (male)"
msgid "th"
msgstr "de"

#: src/humanize/number.py:89
msgctxt "5 (male)"
msgid "th"
msgstr "de"

#: src/humanize/number.py:90
msgctxt "6 (male)"
msgid "th"
msgstr "de"

#: src/humanize/number.py:91
msgctxt "7 (male)"
msgid "th"
msgstr "de"

#: src/humanize/number.py:92
msgctxt "8 (male)"
msgid "th"
msgstr "de"

#: src/humanize/number.py:93
msgctxt "9 (male)"
msgid "th"
msgstr "de"

#: src/humanize/number.py:97
msgctxt "0 (female)"
msgid "th"
msgstr "de"

#: src/humanize/number.py:98
msgctxt "1 (female)"
msgid "st"
msgstr "ste"

#: src/humanize/number.py:99
msgctxt "2 (female)"
msgid "nd"
msgstr "de"

#: src/humanize/number.py:100
msgctxt "3 (female)"
msgid "rd"
msgstr "de"

#: src/humanize/number.py:101
msgctxt "4 (female)"
msgid "th"
msgstr "de"

#: src/humanize/number.py:102
msgctxt "5 (female)"
msgid "th"
msgstr "de"

#: src/humanize/number.py:103
msgctxt "6 (female)"
msgid "th"
msgstr "de"

#: src/humanize/number.py:104
msgctxt "7 (female)"
msgid "th"
msgstr "de"

#: src/humanize/number.py:105
msgctxt "8 (female)"
msgid "th"
msgstr "de"

#: src/humanize/number.py:106
msgctxt "9 (female)"
msgid "th"
msgstr "de"

#: src/humanize/number.py:178
msgid "thousand"
msgid_plural "thousand"
msgstr[0] ""
msgstr[1] ""

#: src/humanize/number.py:179
msgid "million"
msgid_plural "million"
msgstr[0] "miljoen"
msgstr[1] "miljoen"

#: src/humanize/number.py:180
msgid "billion"
msgid_plural "billion"
msgstr[0] "miljard"
msgstr[1] "miljard"

#: src/humanize/number.py:181
msgid "trillion"
msgid_plural "trillion"
msgstr[0] "biljoen"
msgstr[1] "biljoen"

#: src/humanize/number.py:182
msgid "quadrillion"
msgid_plural "quadrillion"
msgstr[0] "biljard"
msgstr[1] "biljard"

#: src/humanize/number.py:183
msgid "quintillion"
msgid_plural "quintillion"
msgstr[0] "triljoen"
msgstr[1] "triljoen"

#: src/humanize/number.py:184
msgid "sextillion"
msgid_plural "sextillion"
msgstr[0] "triljard"
msgstr[1] "triljard"

#: src/humanize/number.py:185
msgid "septillion"
msgid_plural "septillion"
msgstr[0] "quadriljoen"
msgstr[1] "quadriljoen"

#: src/humanize/number.py:186
msgid "octillion"
msgid_plural "octillion"
msgstr[0] "quadriljard"
msgstr[1] "quadriljard"

#: src/humanize/number.py:187
msgid "nonillion"
msgid_plural "nonillion"
msgstr[0] "quintiljoen"
msgstr[1] "quintiljoen"

#: src/humanize/number.py:188
msgid "decillion"
msgid_plural "decillion"
msgstr[0] "quintiljard"
msgstr[1] "quintiljard"

#: src/humanize/number.py:189
msgid "googol"
msgid_plural "googol"
msgstr[0] "googol"
msgstr[1] "googol"

#: src/humanize/number.py:301
msgid "zero"
msgstr "nul"

#: src/humanize/number.py:302
msgid "one"
msgstr "één"

#: src/humanize/number.py:303
msgid "two"
msgstr "twee"

#: src/humanize/number.py:304
msgid "three"
msgstr "drie"

#: src/humanize/number.py:305
msgid "four"
msgstr "vier"

#: src/humanize/number.py:306
msgid "five"
msgstr "vijf"

#: src/humanize/number.py:307
msgid "six"
msgstr "zes"

#: src/humanize/number.py:308
msgid "seven"
msgstr "zeven"

#: src/humanize/number.py:309
msgid "eight"
msgstr "acht"

#: src/humanize/number.py:310
msgid "nine"
msgstr "negen"

#: src/humanize/time.py:152
#, fuzzy, python-format
msgid "%d microsecond"
msgid_plural "%d microseconds"
msgstr[0] "%d microseconde"
msgstr[1] "%d microseconden"

#: src/humanize/time.py:161
#, fuzzy, python-format
msgid "%d millisecond"
msgid_plural "%d milliseconds"
msgstr[0] "%d milliseconde"
msgstr[1] "%d milliseconden"

#: src/humanize/time.py:164 src/humanize/time.py:259
msgid "a moment"
msgstr "een moment"

#: src/humanize/time.py:167
msgid "a second"
msgstr "een seconde"

#: src/humanize/time.py:170
#, python-format
msgid "%d second"
msgid_plural "%d seconds"
msgstr[0] "%d seconde"
msgstr[1] "%d seconden"

#: src/humanize/time.py:173
msgid "a minute"
msgstr "een minuut"

#: src/humanize/time.py:177
#, python-format
msgid "%d minute"
msgid_plural "%d minutes"
msgstr[0] "%d minuut"
msgstr[1] "%d minuten"

#: src/humanize/time.py:180
msgid "an hour"
msgstr "een uur"

#: src/humanize/time.py:184
#, python-format
msgid "%d hour"
msgid_plural "%d hours"
msgstr[0] "%d uur"
msgstr[1] "%d uur"

#: src/humanize/time.py:188
msgid "a day"
msgstr "een dag"

#: src/humanize/time.py:191 src/humanize/time.py:194
#, python-format
msgid "%d day"
msgid_plural "%d days"
msgstr[0] "%d dag"
msgstr[1] "%d dagen"

#: src/humanize/time.py:197
msgid "a month"
msgstr "een maand"

#: src/humanize/time.py:199
#, python-format
msgid "%d month"
msgid_plural "%d months"
msgstr[0] "%d maand"
msgstr[1] "%d maanden"

#: src/humanize/time.py:203
msgid "a year"
msgstr "een jaar"

#: src/humanize/time.py:206 src/humanize/time.py:217
#, python-format
msgid "1 year, %d day"
msgid_plural "1 year, %d days"
msgstr[0] "1 jaar, %d dag"
msgstr[1] "1 jaar, %d dagen"

#: src/humanize/time.py:210
msgid "1 year, 1 month"
msgstr "1 jaar, 1 maand"

#: src/humanize/time.py:213
#, python-format
msgid "1 year, %d month"
msgid_plural "1 year, %d months"
msgstr[0] "1 jaar, %d maand"
msgstr[1] "1 jaar, %d maanden"

#: src/humanize/time.py:219
#, python-format
msgid "%d year"
msgid_plural "%d years"
msgstr[0] "%d jaar"
msgstr[1] "%d jaar"

#: src/humanize/time.py:256
#, python-format
msgid "%s from now"
msgstr "over %s"

#: src/humanize/time.py:256
#, python-format
msgid "%s ago"
msgstr "%s geleden"

#: src/humanize/time.py:260
msgid "now"
msgstr "nu"

#: src/humanize/time.py:284
msgid "today"
msgstr "vandaag"

#: src/humanize/time.py:287
msgid "tomorrow"
msgstr "morgen"

#: src/humanize/time.py:290
msgid "yesterday"
msgstr "gisteren"

#: src/humanize/time.py:600
#, python-format
msgid "%s and %s"
msgstr "%s en %s"
