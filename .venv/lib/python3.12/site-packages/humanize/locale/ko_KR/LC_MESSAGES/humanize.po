# Korean (Korea) translations for humanize.
# Copyright (C) 2013
# This file is distributed under the same license as the humanize project.
# @youngrok, 2013.
#
msgid ""
msgstr ""
"Project-Id-Version: PROJECT VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-01-08 19:22+0200\n"
"PO-Revision-Date: 2013-07-10 11:38+0900\n"
"Last-Translator: @youngrok\n"
"Language-Team: ko_KR <<EMAIL>>\n"
"Language: ko\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"
"Generated-By: Babel 0.9.6\n"
"X-Generator: Poedit 1.5.7\n"

#: src/humanize/number.py:84
#, fuzzy
msgctxt "0 (male)"
msgid "th"
msgstr "번째"

#: src/humanize/number.py:85
#, fuzzy
msgctxt "1 (male)"
msgid "st"
msgstr "번째"

#: src/humanize/number.py:86
#, fuzzy
msgctxt "2 (male)"
msgid "nd"
msgstr "번째"

#: src/humanize/number.py:87
#, fuzzy
msgctxt "3 (male)"
msgid "rd"
msgstr "번째"

#: src/humanize/number.py:88
#, fuzzy
msgctxt "4 (male)"
msgid "th"
msgstr "번째"

#: src/humanize/number.py:89
#, fuzzy
msgctxt "5 (male)"
msgid "th"
msgstr "번째"

#: src/humanize/number.py:90
#, fuzzy
msgctxt "6 (male)"
msgid "th"
msgstr "번째"

#: src/humanize/number.py:91
#, fuzzy
msgctxt "7 (male)"
msgid "th"
msgstr "번째"

#: src/humanize/number.py:92
#, fuzzy
msgctxt "8 (male)"
msgid "th"
msgstr "번째"

#: src/humanize/number.py:93
#, fuzzy
msgctxt "9 (male)"
msgid "th"
msgstr "번째"

#: src/humanize/number.py:97
#, fuzzy
msgctxt "0 (female)"
msgid "th"
msgstr "번째"

#: src/humanize/number.py:98
#, fuzzy
msgctxt "1 (female)"
msgid "st"
msgstr "번째"

#: src/humanize/number.py:99
#, fuzzy
msgctxt "2 (female)"
msgid "nd"
msgstr "번째"

#: src/humanize/number.py:100
#, fuzzy
msgctxt "3 (female)"
msgid "rd"
msgstr "번째"

#: src/humanize/number.py:101
#, fuzzy
msgctxt "4 (female)"
msgid "th"
msgstr "번째"

#: src/humanize/number.py:102
#, fuzzy
msgctxt "5 (female)"
msgid "th"
msgstr "번째"

#: src/humanize/number.py:103
#, fuzzy
msgctxt "6 (female)"
msgid "th"
msgstr "번째"

#: src/humanize/number.py:104
#, fuzzy
msgctxt "7 (female)"
msgid "th"
msgstr "번째"

#: src/humanize/number.py:105
#, fuzzy
msgctxt "8 (female)"
msgid "th"
msgstr "번째"

#: src/humanize/number.py:106
#, fuzzy
msgctxt "9 (female)"
msgid "th"
msgstr "번째"

#: src/humanize/number.py:178
msgid "thousand"
msgid_plural "thousand"
msgstr[0] ""
msgstr[1] ""

#: src/humanize/number.py:179
msgid "million"
msgid_plural "million"
msgstr[0] "%(value)s million"
msgstr[1] "%(value)s million"

#: src/humanize/number.py:180
msgid "billion"
msgid_plural "billion"
msgstr[0] "milliard"
msgstr[1] "milliard"

#: src/humanize/number.py:181
#, fuzzy
msgid "trillion"
msgid_plural "trillion"
msgstr[0] "%(value)s billion"
msgstr[1] "%(value)s billion"

#: src/humanize/number.py:182
#, fuzzy
msgid "quadrillion"
msgid_plural "quadrillion"
msgstr[0] "%(value)s quadrillion"
msgstr[1] "%(value)s quadrillion"

#: src/humanize/number.py:183
#, fuzzy
msgid "quintillion"
msgid_plural "quintillion"
msgstr[0] "%(value)s quintillion"
msgstr[1] "%(value)s quintillion"

#: src/humanize/number.py:184
#, fuzzy
msgid "sextillion"
msgid_plural "sextillion"
msgstr[0] "%(value)s sextillion"
msgstr[1] "%(value)s sextillion"

#: src/humanize/number.py:185
#, fuzzy
msgid "septillion"
msgid_plural "septillion"
msgstr[0] "%(value)s septillion"
msgstr[1] "%(value)s septillion"

#: src/humanize/number.py:186
#, fuzzy
msgid "octillion"
msgid_plural "octillion"
msgstr[0] "%(value)s octillion"
msgstr[1] "%(value)s octillion"

#: src/humanize/number.py:187
#, fuzzy
msgid "nonillion"
msgid_plural "nonillion"
msgstr[0] "%(value)s nonillion"
msgstr[1] "%(value)s nonillion"

#: src/humanize/number.py:188
#, fuzzy
msgid "decillion"
msgid_plural "decillion"
msgstr[0] "%(value)s décillion"
msgstr[1] "%(value)s décillion"

#: src/humanize/number.py:189
#, fuzzy
msgid "googol"
msgid_plural "googol"
msgstr[0] "%(value)s gogol"
msgstr[1] "%(value)s gogol"

#: src/humanize/number.py:301
msgid "zero"
msgstr ""

#: src/humanize/number.py:302
msgid "one"
msgstr "하나"

#: src/humanize/number.py:303
msgid "two"
msgstr "둘"

#: src/humanize/number.py:304
msgid "three"
msgstr "셋"

#: src/humanize/number.py:305
msgid "four"
msgstr "넷"

#: src/humanize/number.py:306
msgid "five"
msgstr "다섯"

#: src/humanize/number.py:307
msgid "six"
msgstr "여섯"

#: src/humanize/number.py:308
msgid "seven"
msgstr "일곱"

#: src/humanize/number.py:309
msgid "eight"
msgstr "여덟"

#: src/humanize/number.py:310
msgid "nine"
msgstr "아홉"

#: src/humanize/time.py:152
#, python-format
msgid "%d microsecond"
msgid_plural "%d microseconds"
msgstr[0] "%d마이크로초"
msgstr[1] "%d마이크로초"

#: src/humanize/time.py:161
#, python-format
msgid "%d millisecond"
msgid_plural "%d milliseconds"
msgstr[0] "%d밀리초"
msgstr[1] "%d밀리초"

#: src/humanize/time.py:164 src/humanize/time.py:259
msgid "a moment"
msgstr "잠깐"

#: src/humanize/time.py:167
msgid "a second"
msgstr "1초"

#: src/humanize/time.py:170
#, python-format
msgid "%d second"
msgid_plural "%d seconds"
msgstr[0] "%d초"
msgstr[1] "%d초"

#: src/humanize/time.py:173
msgid "a minute"
msgstr "1분"

#: src/humanize/time.py:177
#, python-format
msgid "%d minute"
msgid_plural "%d minutes"
msgstr[0] "%d분"
msgstr[1] "%d분"

#: src/humanize/time.py:180
msgid "an hour"
msgstr "1시간"

#: src/humanize/time.py:184
#, python-format
msgid "%d hour"
msgid_plural "%d hours"
msgstr[0] "%d시간"
msgstr[1] "%d시간"

#: src/humanize/time.py:188
msgid "a day"
msgstr "하루"

#: src/humanize/time.py:191 src/humanize/time.py:194
#, python-format
msgid "%d day"
msgid_plural "%d days"
msgstr[0] "%d일"
msgstr[1] "%d일"

#: src/humanize/time.py:197
msgid "a month"
msgstr "1개월"

#: src/humanize/time.py:199
#, python-format
msgid "%d month"
msgid_plural "%d months"
msgstr[0] "%d개월"
msgstr[1] "%d개월"

#: src/humanize/time.py:203
msgid "a year"
msgstr "1년"

#: src/humanize/time.py:206 src/humanize/time.py:217
#, python-format
msgid "1 year, %d day"
msgid_plural "1 year, %d days"
msgstr[0] "1년, %d일"
msgstr[1] "1년, %d일"

#: src/humanize/time.py:210
msgid "1 year, 1 month"
msgstr "1년, 1개월"

#: src/humanize/time.py:213
#, python-format
msgid "1 year, %d month"
msgid_plural "1 year, %d months"
msgstr[0] "1년, %d개월"
msgstr[1] "1년, %d개월"

#: src/humanize/time.py:219
#, python-format
msgid "%d year"
msgid_plural "%d years"
msgstr[0] "%d년"
msgstr[1] "%d년"

#: src/humanize/time.py:256
#, python-format
msgid "%s from now"
msgstr "%s 후"

#: src/humanize/time.py:256
#, python-format
msgid "%s ago"
msgstr "%s 전"

#: src/humanize/time.py:260
msgid "now"
msgstr "방금"

#: src/humanize/time.py:284
msgid "today"
msgstr "오늘"

#: src/humanize/time.py:287
msgid "tomorrow"
msgstr "내일"

#: src/humanize/time.py:290
msgid "yesterday"
msgstr "어제"

#: src/humanize/time.py:600
#, python-format
msgid "%s and %s"
msgstr ""
