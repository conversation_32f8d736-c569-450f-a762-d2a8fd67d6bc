from rest_framework import serializers
from django.contrib.auth.models import User
from .models import JobListing, JobSearch

class JobListingSerializer(serializers.ModelSerializer):
    class Meta:
        model = JobListing
        fields = ['id', 'title', 'company', 'location', 'url', 'posted_date', 'description', 'created_at']

class JobSearchSerializer(serializers.ModelSerializer):
    jobs = JobListingSerializer(many=True, read_only=True)
    # user = UserSerializer(read_only=True)

    class Meta:
        model = JobSearch
        fields = ['id', 'query', 'location', 'created_at', 'task_id', 'jobs', 'user']
        read_only_fields = ['created_at', 'task_id', 'jobs']

class UserSerializer(serializers.ModelSerializer):

    class Meta:
        model = User
        fields = ['id', 'username']
