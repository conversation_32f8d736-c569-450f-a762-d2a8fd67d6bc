from django.db import models
from django.contrib.auth.models import User

# Create your models here.

class JobSearch(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name="searches")
    query = models.CharField(max_length=255)
    location = models.CharField(max_length=255, blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    task_id = models.CharField(max_length=255, blank=True, null=True)

    def __str__(self):
        return f"{self.query} ({self.location}) - {self.id}"

class JobListing(models.Model):
    search = models.ForeignKey(JobSearch, on_delete=models.CASCADE, related_name="jobs")
    title = models.CharField(max_length=255)
    description = models.TextField(blank=True)
    company = models.CharField(max_length=255)
    location = models.CharField(max_length=255)
    url = models.URLField(max_length=1000)
    posted_date = models.Date<PERSON>ield(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        indexes = [
            models.Index(fields=['title']),
            models.Index(fields=['company']),
        ]

    def __str__(self):
        return f"{self.title} @ {self.company}"