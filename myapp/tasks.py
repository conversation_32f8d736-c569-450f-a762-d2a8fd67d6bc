from celery import shared_task, current_task
from .models import JobSearch, JobListing
import requests
from bs4 import BeautifulSoup
from django.db import transaction
from jobscrarify.celery import app as celery_app
import time

HEADERS = {"User-Agent": "Mozilla/5.0 (compatible; JobScraper/1.0)"}

@shared_task(bind=True)
def scrape_jobs(self, search_id):
    """
    Scrapes a couple of example endpoints and stores JobListing rows.
    Replace or extend site-specific parsers as needed.
    """
    try:
        search = JobSearch.objects.get(pk=search_id)
    except JobSearch.DoesNotExist:
        return {"error": "Search not found"}
    
    query = search.query
    location = search.location or ""

    collected = []

    try:
        url = f"https://remoteok.com/remote-{query.replace(' ', '-')}-jobs"
        print(url)
        r = requests.get(url, headers=HEADERS, timeout=15)
        soup = BeautifulSoup(r.text, "html.parser")
        for job_elem in soup.select("tr.job"):
            title_elem = job_elem.select_one("td.position h2")
            company_elem = job_elem.select_one("td.company h3")
            link_elem = job_elem.get("data-url") or job_elem.select_one("a")
            if title_elem and company_elem:
                job_url = ("https://remoteok.com" + link_elem) if isinstance(link_elem, str) else (link_elem['href'] if link_elem else '')
                collected.append({
                    "title": title_elem.get_text(strip=True),
                    "company": company_elem.get_text(strip=True),
                    "location": location or "Remote",
                    "url": job_url,
                    "posted_date": "2000-01-01",
                    "description": ""
                })
    except Exception:
        pass

    try:
        gh_url = f"https://jobs.github.com/positions.json?description={query}&location={location}"
        print(gh_url)
        r = requests.get(gh_url, headers=HEADERS, timeout=10)
        if r.status_code == 200:
            jobs_json = r.json()
            for j in jobs_json:
                collected.append({
                    "title": j.get("title"),
                    "company": j.get("company"),
                    "location": j.get("location") or location,
                    "url": j.get("url") or j.get("company_url") or "",
                    "posted_date": j.get("created_at") or "2000-01-01",
                    "description": j.get("description") or ""
                })
    except Exception:
        pass

    objs = []
    for item in collected:
        objs.append(JobListing(
            search=search, title=item['title'] or "No Title", company=item['company'] or "", location=item['location'] or "", url=item['url'] or "", posted_date=item.get('posted_date', ''), description=item.get('description', '')
        ))
    
    if objs:
        JobListing.objects.bulk_create(objs)

    return {"count": len(objs)}